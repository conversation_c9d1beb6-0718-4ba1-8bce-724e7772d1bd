package com.sub.subm.api.service.assistant.model;

import java.util.ArrayList;
import java.util.List;

import org.springframework.stereotype.Component;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sub.subm.dal.dao.chat.ChatMemoryDao;
import com.sub.subm.dal.entity.generate.chat.ChatMemoryPo;

import dev.langchain4j.agent.tool.ToolExecutionRequest;
import dev.langchain4j.data.message.ChatMessage;
import dev.langchain4j.store.memory.chat.ChatMemoryStore;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * 持久化聊天记忆存储实现
 * 支持多用户/多会话的聊天记忆管理
 * 数据存储在数据库中
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class PersistentChatMemoryStore implements ChatMemoryStore {


    private final ChatMemoryDao chatMemoryDao;
    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public List<ChatMessage> getMessages(Object memoryId) {
        log.debug("获取聊天记忆, memoryId: {}", memoryId);

        try {
            String sessionId = memoryId.toString();
            ChatMemoryPo chatMemory = chatMemoryDao.selectBySessionId(sessionId);

            if (chatMemory == null || !chatMemory.isValid()) {
                log.debug("memoryId {} 没有历史消息，返回空列表", memoryId);
                return new ArrayList<>();
            }

            // 如果messages字段为空（新创建的session），返回空列表
            if (chatMemory.getMessages() == null || chatMemory.getMessages().trim().isEmpty()) {
                log.debug("memoryId {} 的messages字段为空，返回空列表", memoryId);
                return new ArrayList<>();
            }

            // 反序列化消息列表
            List<ChatMessage> messages = deserializeMessages(chatMemory.getMessages());
            log.debug("memoryId {} 找到 {} 条历史消息", memoryId, messages.size());
            return messages;

        } catch (Exception e) {
            log.error("获取聊天记忆失败, memoryId: {}", memoryId, e);
            return new ArrayList<>();
        }
    }

    @Override
    public void updateMessages(Object memoryId, List<ChatMessage> messages) {
        log.debug("更新聊天记忆, memoryId: {}, 消息数量: {}", memoryId, messages != null ? messages.size() : 0);

        try {
            String sessionId = memoryId.toString();

            // 查询session记录（应该已经存在）
            ChatMemoryPo existingMemory = chatMemoryDao.selectBySessionId(sessionId);

            if (existingMemory == null || !existingMemory.isValid()) {
                log.warn("session记录不存在或已删除, sessionId: {}", sessionId);
                return;
            }

            if (messages == null || messages.isEmpty()) {
                // 清空消息，但保留session记录
                existingMemory.setMessages("");
                existingMemory.updateMessageCount(0);
                chatMemoryDao.update(existingMemory);
                log.debug("清空聊天记忆, sessionId: {}", sessionId);
                return;
            }

            // 序列化消息列表并更新记录
            String messagesJson = serializeMessages(messages);
            existingMemory.setMessages(messagesJson);
            existingMemory.updateMessageCount(messages.size());
            chatMemoryDao.update(existingMemory);
            log.debug("更新聊天记忆, sessionId: {}, 消息数量: {}", sessionId, messages.size());

        } catch (Exception e) {
            log.error("更新聊天记忆失败, memoryId: {}", memoryId, e);
        }
    }

    @Override
    public void deleteMessages(Object memoryId) {
        log.debug("删除聊天记忆, memoryId: {}", memoryId);

        try {
            String sessionId = memoryId.toString();
            ChatMemoryPo existingMemory = chatMemoryDao.selectBySessionId(sessionId);

            if (existingMemory != null && existingMemory.isValid()) {
                chatMemoryDao.softDeleteBySessionId(sessionId);
                log.info("成功删除memoryId {} 的聊天记忆", memoryId);
            } else {
                log.debug("memoryId {} 的聊天记忆不存在或已删除", memoryId);
            }
        } catch (Exception e) {
            log.error("删除聊天记忆失败, memoryId: {}", memoryId, e);
        }
    }

    /**
     * 获取当前存储的会话数量
     */
    public int getSessionCount() {
        try {
            return chatMemoryDao.countTotal();
        } catch (Exception e) {
            log.error("获取会话数量失败", e);
            return 0;
        }
    }

    /**
     * 清空所有聊天记忆
     */
    public void clearAll() {
        try {
            int count = chatMemoryDao.countTotal();
            // 这里可以实现批量软删除，但需要添加相应的DAO方法
            log.warn("clearAll方法暂未实现批量删除，当前会话数量: {}", count);
        } catch (Exception e) {
            log.error("清空聊天记忆失败", e);
        }
    }

    /**
     * 序列化消息列表为JSON字符串
     */
    private String serializeMessages(List<ChatMessage> messages) {
        try {
            List<ChatMessageDto> dtoList = new ArrayList<>();
            for (ChatMessage message : messages) {
                ChatMessageDto dto = convertToDto(message);
                dtoList.add(dto);
            }

            return objectMapper.writeValueAsString(dtoList);
        } catch (Exception e) {
            log.error("序列化消息列表失败", e);
            throw new RuntimeException("序列化消息列表失败", e);
        }
    }

    /**
     * 反序列化JSON字符串为消息列表
     */
    private List<ChatMessage> deserializeMessages(String messagesJson) {
        try {
            if (messagesJson == null || messagesJson.trim().isEmpty()) {
                return new ArrayList<>();
            }

            // 使用ChatMessageDto反序列化
            TypeReference<List<ChatMessageDto>> typeRef = new TypeReference<List<ChatMessageDto>>() {};
            List<ChatMessageDto> dtoList = objectMapper.readValue(messagesJson, typeRef);

            List<ChatMessage> messages = new ArrayList<>();
            for (ChatMessageDto dto : dtoList) {
                ChatMessage message = convertFromDto(dto);
                if (message != null) {
                    messages.add(message);
                }
            }

            return messages;
        } catch (Exception e) {
            log.error("反序列化消息列表失败, json: {}", messagesJson, e);
            return new ArrayList<>();
        }
    }

    /**
     * 将ChatMessage转换为ChatMessageDto
     */
    private ChatMessageDto convertToDto(ChatMessage message) {
        ChatMessageDto dto = new ChatMessageDto();
        dto.setType(message.type().toString());
        dto.setTimestamp(System.currentTimeMillis());

        if (message instanceof dev.langchain4j.data.message.UserMessage) {
            dev.langchain4j.data.message.UserMessage userMsg = (dev.langchain4j.data.message.UserMessage) message;
            dto.setText(userMsg.singleText());
            dto.setName(userMsg.name());

        } else if (message instanceof dev.langchain4j.data.message.AiMessage) {
            dev.langchain4j.data.message.AiMessage aiMsg = (dev.langchain4j.data.message.AiMessage) message;
            dto.setText(aiMsg.text());

            // 处理工具执行请求
            if (aiMsg.hasToolExecutionRequests()) {
                List<ChatMessageDto.ToolExecutionRequestDto> toolRequests = new ArrayList<>();
                for (ToolExecutionRequest request : aiMsg.toolExecutionRequests()) {
                    ChatMessageDto.ToolExecutionRequestDto toolDto = new ChatMessageDto.ToolExecutionRequestDto();
                    toolDto.setId(request.id());
                    toolDto.setName(request.name());
                    toolDto.setArguments(request.arguments());
                    toolRequests.add(toolDto);
                }
                dto.setToolExecutionRequests(toolRequests);
            }

        } else if (message instanceof dev.langchain4j.data.message.SystemMessage) {
            dev.langchain4j.data.message.SystemMessage sysMsg = (dev.langchain4j.data.message.SystemMessage) message;
            dto.setText(sysMsg.text());

        } else if (message instanceof dev.langchain4j.data.message.ToolExecutionResultMessage) {
            dev.langchain4j.data.message.ToolExecutionResultMessage toolMsg = (dev.langchain4j.data.message.ToolExecutionResultMessage) message;
            dto.setText(toolMsg.text());
            dto.setToolId(toolMsg.id());
            dto.setToolName(toolMsg.toolName());
            dto.setToolExecutionResult(toolMsg.text());
        }

        return dto;
    }

    /**
     * 将ChatMessageDto转换为ChatMessage
     */
    private ChatMessage convertFromDto(ChatMessageDto dto) {
        try {
            switch (dto.getType()) {
                case "USER":
                    if (dto.getName() != null) {
                        return dev.langchain4j.data.message.UserMessage.from(dto.getName(), dto.getText());
                    } else {
                        return dev.langchain4j.data.message.UserMessage.from(dto.getText());
                    }

                case "AI":
                    if (dto.getToolExecutionRequests() != null && !dto.getToolExecutionRequests().isEmpty()) {
                        // 有工具调用的AI消息
                        List<ToolExecutionRequest> requests = new ArrayList<>();
                        for (ChatMessageDto.ToolExecutionRequestDto toolDto : dto.getToolExecutionRequests()) {
                            ToolExecutionRequest request =
                                ToolExecutionRequest.builder()
                                    .id(toolDto.getId())
                                    .name(toolDto.getName())
                                    .arguments(toolDto.getArguments())
                                    .build();
                            requests.add(request);
                        }
                        return dev.langchain4j.data.message.AiMessage.from(dto.getText(), requests);
                    } else {
                        return dev.langchain4j.data.message.AiMessage.from(dto.getText());
                    }
                    
                case "SYSTEM":
                    return dev.langchain4j.data.message.SystemMessage.from(dto.getText());

                case "TOOL_EXECUTION_RESULT":
                    return dev.langchain4j.data.message.ToolExecutionResultMessage.from(
                        dto.getToolId(), dto.getToolName(), dto.getToolExecutionResult());

                default:
                    log.warn("未知的消息类型: {}", dto.getType());
                    return dev.langchain4j.data.message.AiMessage.from(dto.getText());
            }
        } catch (Exception e) {
            log.error("转换ChatMessageDto失败: {}", dto, e);
            return null;
        }
    }
}
