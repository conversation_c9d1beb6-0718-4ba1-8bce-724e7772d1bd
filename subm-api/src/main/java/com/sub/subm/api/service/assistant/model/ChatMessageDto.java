package com.sub.subm.api.service.assistant.model;

import java.util.List;

import lombok.Data;

/**
 * 聊天消息DTO - 用于序列化ChatMessage
 */
@Data
public class ChatMessageDto {
    
    /**
     * 消息类型 (USER, AI, SYSTEM, TOOL_EXECUTION_REQUEST, TOOL_EXECUTION_RESULT)
     */
    private String type;
    
    /**
     * 消息文本内容
     */
    private String text;
    
    /**
     * 用户名称 (仅UserMessage有)
     */
    private String name;
    
    /**
     * 工具执行请求列表 (仅AiMessage有)
     */
    private List<ToolExecutionRequestDto> toolExecutionRequests;
    
    /**
     * 工具ID (仅ToolExecutionResultMessage有)
     */
    private String toolId;
    
    /**
     * 工具名称 (仅ToolExecutionResultMessage有)
     */
    private String toolName;
    
    /**
     * 工具执行结果 (仅ToolExecutionResultMessage有)
     */
    private String toolExecutionResult;
    
    /**
     * 创建时间戳
     */
    private Long timestamp;
    
    /**
     * 工具执行请求DTO
     */
    @Data
    public static class ToolExecutionRequestDto {
        private String id;
        private String name;
        private String arguments;
    }
}
