package com.sub.subm.api.service.assistant.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import dev.langchain4j.model.openai.OpenAiChatModel;
import dev.langchain4j.model.openai.OpenAiStreamingChatModel;

/**
 * Lang<PERSON>hain4j模型配置
 */
@Configuration
public class ModelConfig {

    private final String openAiApiBaseUrl = "https://dashscope.aliyuncs.com/compatible-mode/v1";

    private final String openAiApiKey = "sk-0c8238884ecd4826aa4928df2dcd3632";

    private final String openAiModel = "qwen-max-latest";

    @Bean
    public OpenAiStreamingChatModel openAiStreamingChatModel() {
        return OpenAiStreamingChatModel.builder()
                .baseUrl(openAiApiBaseUrl)
                .apiKey(openAiApiKey)
                .modelName(openAiModel)
                .temperature(0.7)
                .logRequests(true)
                .logResponses(true)
                .build();
    }

    @Bean
    public OpenAiChatModel openAiChatModel(){
        return OpenAiChatModel.builder()
                .baseUrl(openAiApiBaseUrl)
                .apiKey(openAiApiKey)
                .modelName(openAiModel)
                .temperature(0.7)
                .logRequests(true)
                .logResponses(true)
                .build();
    }
}
