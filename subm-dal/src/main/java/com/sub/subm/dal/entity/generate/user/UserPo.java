package com.sub.subm.dal.entity.generate.user;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.sub.common.constant.UserConstants;
import com.sub.common.core.common.BaseEntity;
import com.sub.common.utils.StringUtils;
import com.sub.common.utils.TimeUtils;
import com.sub.common.utils.ip.NetworkUtil;
import com.sub.common.utils.serializer.IsSettingSerializer;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "用户数据")
public class UserPo extends BaseEntity implements Serializable {
    @Schema(description = "手机号码")
    private String phone;
    @Schema(description = "邮箱")
    private String email;
    @Schema(description = "google账号Id")
    @JsonSerialize(using = IsSettingSerializer.class)
    private String googleId;
    @Schema(description = "微信账号id")
    @JsonSerialize(using = IsSettingSerializer.class)
    private String wechatId;
    @Schema(description = "苹果账号id")
    @JsonSerialize(using = IsSettingSerializer.class)
    private String appleId;


    @Schema(description = "头像")
    private String avatar;
    @Schema(description = "描述")
    private String motto;
    @Schema(description = "权限即套餐")
    private String roleId;


    @Schema(description = "最后活跃时间")
    private Long lastOnlineTime;
    @Schema(description = "注册ip")
    private String createIp;
    @Schema(description = "活跃ip")
    private String ipAddress;


    @Schema(description = "密码")
    @JsonSerialize(using = IsSettingSerializer.class)
    private String password;
    @Schema(description = "密码盐")
    @JsonIgnore
    private String salt;

    @Schema(description = "设备")
    private String deviceId;

    @Schema(description = "用户偏好货币")
    private String currency;

    /**
     * 根据渠道 判断是账号 还是手机, 邮箱
     */
    public static UserPo resultChannelAccount(UserPo userPo, String uuid,
                                              String account, Integer channel) {
        switch (channel) {
            case 0:
                userPo.setEmail(account);
                break;
            case 1:
                userPo.setPhone(account);
                break;
            case 2:
                userPo.setEmail(account);
                userPo.setGoogleId(uuid);
                break;
            case 3:
                userPo.setWechatId(account);
                break;
            case 4:
                userPo.setEmail(account);
                userPo.setAppleId(uuid);
                break;
        }
        return userPo;
    }

    /**
     * 绑定用户账号
     */
    public static UserPo buildChannelUserPo(UserPo userPo, String uuid,
                                            String account, Integer channel) {
        switch (channel) {
            case 0:
                userPo.setEmail(account);
                break;
            case 1:
                userPo.setPhone(account);
                break;
            case 2:
                userPo.setGoogleId(uuid);
                break;
            case 3:
                userPo.setWechatId(account);
                break;
            case 4:
                userPo.setAppleId(uuid);
                break;
        }
        return userPo;
    }


    /**
     * 解绑账号, 手机, 邮箱
     */
    public static UserPo unBindChannelAccount(UserPo userPo,  Integer channel) {
        String empty = "";
        switch (channel) {
            case 0:
                userPo.setEmail(empty);
                break;
            case 1:
                userPo.setPhone(empty);
                break;
            case 2:
                userPo.setGoogleId(empty);
                break;
            case 3:
                userPo.setWechatId(empty);
                break;
            case 4:
                userPo.setAppleId(empty);
                break;
        }
        return userPo;
    }

    /**
     * 注册
     */
    public static UserPo registerUser(String userName,
                                      String avatar,
                                      String uuid,
                                      String account,
                                      Integer channel) {
        UserPo register = new UserPo();
        register.setRoleId(UserConstants.REGISTER_USER_ROLE);
        register.setIpAddress(NetworkUtil.getIpAddr());
        register.setCreateIp(register.getIpAddress());
        register.setCreateTime(TimeUtils.getCurrentTime());
        register.setLastOnlineTime(register.getCreateTime());
        register.setAvatar(avatar);
        register.setEnable(1);
        register.setCurrency("USD");
        if (StringUtils.isNotEmpty(userName)) {
            register.setName(userName);
        } else {
            register.setName("Sub" + StringUtils.verifySalt(2));
        }
        register = resultChannelAccount(register, uuid, account, channel);
        return register;
    }


}
