package com.sub.common.mail;


import java.io.UnsupportedEncodingException;
import java.util.Properties;
import java.util.concurrent.TimeUnit;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mail.MailException;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.JavaMailSenderImpl;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson2.JSONObject;
import com.sub.common.core.redis.RedisCache;

import jakarta.mail.MessagingException;
import jakarta.mail.internet.InternetAddress;
import jakarta.mail.internet.MimeMessage;

@Component
public class MailUtil {
    private Logger log = LoggerFactory.getLogger(MailUtil.class);


    // 发件配置
    protected JavaMailSender javaMailSender;
    // 系统默认
    private static String systemSendAccount;
    private static String personal;

    @Autowired
    private RedisCache redisCache;


    /**
     * 配置发件邮箱基础配置信息
     * @param configMailPo
     */
    public void initConfigEmail(ConfigMailPo configMailPo) {
        log.info("加载初始化邮箱配置, 配置数据如下==>>>{}", JSONObject.toJSONString(configMailPo));
        systemSendAccount = "<EMAIL>";
        this.javaMailSender = configMailInfo(configMailPo);
        personal = "subm";
    } 

    /**
     * 初始化邮件数据
     * @param configMailPo  邮箱数据
     */
    public JavaMailSender configMailInfo(ConfigMailPo configMailPo){
        log.info(">>>>>>>>>>>>配置发件邮箱");
        JavaMailSenderImpl mailSender = new JavaMailSenderImpl();
        mailSender.setDefaultEncoding("UTF-8");
        mailSender.setHost(configMailPo.getHost());
        mailSender.setPort(configMailPo.getPort());
        mailSender.setUsername(configMailPo.getAccount());
        mailSender.setPassword(configMailPo.getPassword());
        mailSender.setProtocol(configMailPo.getProtocol());
        Properties properties = mailSender.getJavaMailProperties();
        properties.put("mail.smtp.auth", "true");
        properties.put("mail.smtp.timeout", "10000");
        properties.put("mail.smtp.starttls.enable", "true");
        return mailSender;
    }

    @Async
    public void sendEmail(SendMailBo requestEmailModel) {
        log.info("正在进行异步发送邮件, 接受邮件的账号为==>>>{}, 本次发件主题为==>>>{}",
                requestEmailModel.getAccount(),
                requestEmailModel.getSubject());
        try {
            MimeMessage message = javaMailSender.createMimeMessage();
            InternetAddress from = new InternetAddress(systemSendAccount, personal);
            message.setFrom(from);
            MimeMessageHelper helper = new MimeMessageHelper(message, true);
            helper.setTo(String.valueOf(requestEmailModel.getAccount()));
            helper.setSubject(requestEmailModel.getSubject());
            helper.setText(requestEmailModel.getText(), true);
            javaMailSender.send(message);
            redisCache.setCacheObject(requestEmailModel.getRedisKey(), requestEmailModel.getRedisValue(), 5, TimeUnit.MINUTES);
        } catch (MailException | MessagingException | UnsupportedEncodingException e) {
            log.error("发送邮件失败!!! 请检查网络是否存在波动或邮件地址不存在");
            e.printStackTrace();
        }
    }


}
